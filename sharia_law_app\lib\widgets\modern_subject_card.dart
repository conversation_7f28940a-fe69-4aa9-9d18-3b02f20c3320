import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/subject.dart';

class ModernSubjectCard extends StatefulWidget {
  final Subject subject;
  final VoidCallback onTap;

  const ModernSubjectCard({
    super.key,
    required this.subject,
    required this.onTap,
  });

  @override
  State<ModernSubjectCard> createState() => _ModernSubjectCardState();
}

class _ModernSubjectCardState extends State<ModernSubjectCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: widget.onTap,
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE5E7EB), width: 0.5),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: Row(
                  children: [
                    // سهم الرجوع
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF8F9FA),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios,
                        color: Color(0xFF8E8E93),
                        size: 12,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // معلومات المادة
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.subject.arabicName,
                            style: GoogleFonts.cairo(
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF1D1D1F),
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.right,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              _buildInfoItem(
                                icon: Icons.assignment_outlined,
                                count: widget.subject.examsCount,
                                label: 'امتحانات',
                                color: const Color(0xFF007AFF),
                              ),
                              const SizedBox(width: 16),
                              _buildInfoItem(
                                icon: Icons.play_circle_outline,
                                count: widget.subject.lecturesCount,
                                label: 'محاضرات',
                                color: const Color(0xFF34C759),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(3),
          ),
          child: Icon(icon, color: color, size: 10),
        ),
        const SizedBox(width: 4),
        Text(
          '$count',
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w600,
            color: const Color(0xFF1D1D1F),
            fontSize: 13,
          ),
        ),
        const SizedBox(width: 2),
        Text(
          label,
          style: GoogleFonts.cairo(
            color: const Color(0xFF8E8E93),
            fontSize: 12,
          ),
        ),
      ],
    );
  }
}
