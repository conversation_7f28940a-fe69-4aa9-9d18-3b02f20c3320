import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

void main() {
  runApp(const ShariaLawApp());
}

class ShariaLawApp extends StatelessWidget {
  const ShariaLawApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق الشريعة والقانون',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF6366F1),
          brightness: Brightness.light,
        ),
        fontFamily: GoogleFonts.cairo().fontFamily,
      ),
      home: const MainScreen(),
      builder: (context, child) {
        SystemChrome.setSystemUIOverlayStyle(
          const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarColor: Colors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
          ),
        );
        return Directionality(textDirection: TextDirection.rtl, child: child!);
      },
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CommunityScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: Container(
        height: 80,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) => setState(() => _currentIndex = index),
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: const Color(0xFF6366F1),
            unselectedItemColor: const Color(0xFF9CA3AF),
            selectedLabelStyle: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home_rounded),
                label: 'الرئيسية',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.people_rounded),
                label: 'المجتمع',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person_rounded),
                label: 'الملف الشخصي',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Academic Year Model
class AcademicYear {
  final String title;
  final String description;
  final List<Semester> semesters;
  final Color color;

  AcademicYear({
    required this.title,
    required this.description,
    required this.semesters,
    required this.color,
  });
}

// Semester Model
class Semester {
  final String name;
  final List<Subject> subjects;

  Semester({required this.name, required this.subjects});
}

// Subject Model
class Subject {
  final String name;
  final String code;
  final int creditHours;

  Subject({required this.name, required this.code, required this.creditHours});
}

// Post Model
class Post {
  final String id;
  final String authorName;
  final String content;
  final DateTime timestamp;
  int likes;
  int comments;
  int shares;
  bool isLiked;
  final String? userRole;
  final bool isPinned;

  Post({
    required this.id,
    required this.authorName,
    required this.content,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.isLiked,
    this.userRole,
    required this.isPinned,
  });
}

// Home Screen
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final List<AcademicYear> academicYears = [
    AcademicYear(
      title: 'الفرقة الأولى',
      description: 'السنة الأولى - المواد الأساسية',
      color: const Color(0xFF6366F1),
      semesters: [
        Semester(
          name: 'الفصل الدراسي الأول',
          subjects: [
            Subject(
              name: 'مدخل إلى علوم الشريعة',
              code: 'SH101',
              creditHours: 3,
            ),
            Subject(
              name: 'القرآن الكريم وعلومه (1)',
              code: 'QR101',
              creditHours: 3,
            ),
            Subject(
              name: 'الحديث الشريف وعلومه (1)',
              code: 'HD101',
              creditHours: 3,
            ),
            Subject(name: 'اللغة العربية (1)', code: 'AR101', creditHours: 3),
            Subject(name: 'مدخل إلى القانون', code: 'LW101', creditHours: 3),
            Subject(
              name: 'تاريخ التشريع الإسلامي',
              code: 'HI101',
              creditHours: 2,
            ),
          ],
        ),
        Semester(
          name: 'الفصل الدراسي الثاني',
          subjects: [
            Subject(name: 'أصول الفقه (1)', code: 'FQ102', creditHours: 3),
            Subject(
              name: 'القرآن الكريم وعلومه (2)',
              code: 'QR102',
              creditHours: 3,
            ),
            Subject(
              name: 'الحديث الشريف وعلومه (2)',
              code: 'HD102',
              creditHours: 3,
            ),
            Subject(name: 'اللغة العربية (2)', code: 'AR102', creditHours: 3),
            Subject(name: 'القانون الدستوري', code: 'LW102', creditHours: 3),
            Subject(
              name: 'المدخل إلى الفقه الإسلامي',
              code: 'FQ101',
              creditHours: 2,
            ),
          ],
        ),
      ],
    ),
    AcademicYear(
      title: 'الفرقة الثانية',
      description: 'السنة الثانية - التخصص المتوسط',
      color: const Color(0xFFEC4899),
      semesters: [
        Semester(
          name: 'الفصل الدراسي الأول',
          subjects: [
            Subject(name: 'فقه العبادات', code: 'FQ201', creditHours: 4),
            Subject(name: 'أصول الفقه (2)', code: 'FQ202', creditHours: 3),
            Subject(name: 'القانون المدني (1)', code: 'LW201', creditHours: 4),
            Subject(name: 'القانون الإداري (1)', code: 'LW202', creditHours: 3),
            Subject(
              name: 'التفسير وعلوم القرآن',
              code: 'QR201',
              creditHours: 3,
            ),
            Subject(name: 'علوم الحديث', code: 'HD201', creditHours: 2),
          ],
        ),
        Semester(
          name: 'الفصل الدراسي الثاني',
          subjects: [
            Subject(name: 'فقه المعاملات (1)', code: 'FQ203', creditHours: 4),
            Subject(name: 'أصول الفقه (3)', code: 'FQ204', creditHours: 3),
            Subject(name: 'القانون المدني (2)', code: 'LW203', creditHours: 4),
            Subject(name: 'القانون الإداري (2)', code: 'LW204', creditHours: 3),
            Subject(name: 'الفقه المقارن', code: 'FQ205', creditHours: 3),
            Subject(name: 'أصول التفسير', code: 'QR202', creditHours: 2),
          ],
        ),
      ],
    ),
    AcademicYear(
      title: 'الفرقة الثالثة',
      description: 'السنة الثالثة - التخصص المتقدم',
      color: const Color(0xFF10B981),
      semesters: [
        Semester(
          name: 'الفصل الدراسي الأول',
          subjects: [
            Subject(name: 'فقه المعاملات (2)', code: 'FQ301', creditHours: 4),
            Subject(name: 'القانون الجنائي (1)', code: 'LW301', creditHours: 4),
            Subject(
              name: 'قانون الأحوال الشخصية',
              code: 'LW302',
              creditHours: 3,
            ),
            Subject(name: 'أصول الفقه (4)', code: 'FQ302', creditHours: 3),
            Subject(
              name: 'الفقه الجنائي الإسلامي',
              code: 'FQ303',
              creditHours: 3,
            ),
            Subject(name: 'القضاء في الإسلام', code: 'FQ304', creditHours: 2),
          ],
        ),
        Semester(
          name: 'الفصل الدراسي الثاني',
          subjects: [
            Subject(name: 'فقه الأسرة', code: 'FQ305', creditHours: 4),
            Subject(name: 'القانون الجنائي (2)', code: 'LW303', creditHours: 4),
            Subject(
              name: 'قانون المرافعات المدنية',
              code: 'LW304',
              creditHours: 3,
            ),
            Subject(
              name: 'الفقه المالي الإسلامي',
              code: 'FQ306',
              creditHours: 3,
            ),
            Subject(name: 'القانون التجاري', code: 'LW305', creditHours: 3),
            Subject(name: 'الوقف والوصايا', code: 'FQ307', creditHours: 2),
          ],
        ),
      ],
    ),
    AcademicYear(
      title: 'الفرقة الرابعة',
      description: 'السنة الرابعة - التخصص والتطبيق',
      color: const Color(0xFFF59E0B),
      semesters: [
        Semester(
          name: 'الفصل الدراسي الأول',
          subjects: [
            Subject(
              name: 'فقه النوازل المعاصرة',
              code: 'FQ401',
              creditHours: 4,
            ),
            Subject(
              name: 'قانون المرافعات الجنائية',
              code: 'LW401',
              creditHours: 4,
            ),
            Subject(
              name: 'القانون الدولي العام',
              code: 'LW402',
              creditHours: 3,
            ),
            Subject(name: 'الاقتصاد الإسلامي', code: 'EC401', creditHours: 3),
            Subject(
              name: 'القانون الدولي الخاص',
              code: 'LW403',
              creditHours: 3,
            ),
            Subject(name: 'منهج البحث العلمي', code: 'RS401', creditHours: 2),
          ],
        ),
        Semester(
          name: 'الفصل الدراسي الثاني',
          subjects: [
            Subject(name: 'مشروع التخرج', code: 'PR402', creditHours: 4),
            Subject(
              name: 'القانون الإداري المتقدم',
              code: 'LW404',
              creditHours: 3,
            ),
            Subject(name: 'فقه البيئة والطبيعة', code: 'FQ402', creditHours: 3),
            Subject(
              name: 'حقوق الإنسان في الإسلام',
              code: 'HR401',
              creditHours: 3,
            ),
            Subject(name: 'القانون المقارن', code: 'LW405', creditHours: 3),
            Subject(name: 'التدريب العملي', code: 'TR401', creditHours: 2),
          ],
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // Modern Hero Header
            SliverToBoxAdapter(
              child: Container(
                height: 280,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF667EEA),
                      const Color(0xFF764BA2),
                      const Color(0xFF6366F1),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(32),
                    bottomRight: Radius.circular(32),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // Header Content
                    Positioned(
                      top: 20,
                      left: 20,
                      right: 20,
                      child: Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.2),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'أهلاً وسهلاً! 🌟',
                                    style: GoogleFonts.cairo(
                                      fontSize: 28,
                                      fontWeight: FontWeight.w800,
                                      color: Colors.white,
                                      height: 1.2,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'مرحباً بك في تطبيق كلية الشريعة والقانون',
                                    style: GoogleFonts.cairo(
                                      fontSize: 14,
                                      color: Colors.white.withValues(
                                        alpha: 0.9,
                                      ),
                                      height: 1.4,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Notification Button
                            Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: IconButton(
                                onPressed: () => _showNotifications(),
                                icon: Stack(
                                  children: [
                                    const Icon(
                                      Icons.notifications_rounded,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                    Positioned(
                                      right: 2,
                                      top: 2,
                                      child: Container(
                                        width: 10,
                                        height: 10,
                                        decoration: BoxDecoration(
                                          color: const Color(0xFFFF4757),
                                          borderRadius: BorderRadius.circular(
                                            5,
                                          ),
                                          border: Border.all(
                                            color: Colors.white,
                                            width: 1,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Stats Cards
                    Positioned(
                      bottom: -30,
                      left: 20,
                      right: 20,
                      child: Row(
                        children: [
                          Expanded(
                            child: _buildSimpleStatCard(
                              icon: Icons.school_rounded,
                              title: '4',
                              subtitle: 'فرق دراسية',
                              color: const Color(0xFF6366F1),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildSimpleStatCard(
                              icon: Icons.auto_stories_rounded,
                              title: '50+',
                              subtitle: 'مادة دراسية',
                              color: const Color(0xFFEC4899),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildSimpleStatCard(
                              icon: Icons.picture_as_pdf_rounded,
                              title: '200+',
                              subtitle: 'ملف PDF',
                              color: const Color(0xFF10B981),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Spacing
            const SliverToBoxAdapter(child: SizedBox(height: 50)),

            // Section Title
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الفرق الدراسية',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'اختر الفرقة الدراسية للوصول إلى المواد والمحاضرات',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        color: const Color(0xFF6B7280),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),

            // Academic Years List
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  final year = academicYears[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildSimpleYearCard(year, index),
                  );
                }, childCount: academicYears.length),
              ),
            ),

            // Bottom spacing
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleStatCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.w800,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: const Color(0xFF6B7280),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleYearCard(AcademicYear year, int index) {
    return GestureDetector(
      onTap: () => _navigateToYear(year),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [year.color, year.color.withValues(alpha: 0.8)],
          ),
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: year.color.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    year.title,
                    style: GoogleFonts.cairo(
                      fontSize: 22,
                      fontWeight: FontWeight.w800,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    year.description,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '${year.semesters.length} فصول دراسية',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_forward_rounded,
                color: Colors.white,
                size: 28,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.notifications, color: Color(0xFF6366F1)),
                SizedBox(width: 8),
                Text('الإشعارات'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [Text('لا توجد إشعارات جديدة')],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _navigateToYear(AcademicYear year) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => YearDetailsScreen(year: year)),
    );
  }
}

// Community Screen
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  final List<Post> posts = [
    Post(
      id: '1',
      authorName: 'أحمد محمد',
      content:
          'مرحباً بالجميع! هل يمكن أن يساعدني أحد في فهم موضوع أصول الفقه؟',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      likes: 15,
      comments: 8,
      shares: 3,
      isLiked: false,
      userRole: 'طالب',
      isPinned: false,
    ),
    Post(
      id: '2',
      authorName: 'د. فاطمة السيد',
      content:
          'تذكير: موعد تسليم بحث القانون المدني هو يوم الأحد القادم. لا تنسوا المراجع المطلوبة.',
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      likes: 42,
      comments: 12,
      shares: 8,
      isLiked: true,
      userRole: 'أستاذ',
      isPinned: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // Modern Header
            SliverToBoxAdapter(
              child: Container(
                height: 200,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF667EEA),
                      const Color(0xFF764BA2),
                      const Color(0xFF6366F1),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(32),
                    bottomRight: Radius.circular(32),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              'مجتمع الطلاب',
                              style: GoogleFonts.cairo(
                                fontSize: 28,
                                fontWeight: FontWeight.w800,
                                color: Colors.white,
                              ),
                            ),
                          ),
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: const Icon(
                              Icons.people_rounded,
                              color: Colors.white,
                              size: 28,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'شارك أفكارك وتفاعل مع زملائك',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                      const Spacer(),
                      // Create Post Button
                      GestureDetector(
                        onTap: () => _showCreatePostDialog(),
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 20,
                                backgroundColor: Colors.white.withValues(
                                  alpha: 0.2,
                                ),
                                child: const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'ما الذي تفكر فيه؟',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.white.withValues(alpha: 0.8),
                                  ),
                                ),
                              ),
                              Icon(
                                Icons.edit_rounded,
                                color: Colors.white.withValues(alpha: 0.8),
                                size: 20,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Posts List
            SliverPadding(
              padding: const EdgeInsets.all(20),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  final post = posts[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: _buildModernPostCard(post),
                  );
                }, childCount: posts.length),
              ),
            ),

            // Bottom spacing
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Widget _buildModernPostCard(Post post) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: const Color(
                    0xFF6366F1,
                  ).withValues(alpha: 0.1),
                  child: Text(
                    post.authorName[0],
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF6366F1),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            post.authorName,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              color: const Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  post.userRole == 'أستاذ'
                                      ? const Color(0xFF10B981)
                                      : const Color(0xFF6366F1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              post.userRole ?? 'طالب',
                              style: GoogleFonts.cairo(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatTimestamp(post.timestamp),
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: const Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ),
                if (post.isPinned)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.push_pin_rounded,
                      size: 16,
                      color: Color(0xFFF59E0B),
                    ),
                  ),
              ],
            ),
          ),

          // Post Content
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              post.content,
              style: GoogleFonts.cairo(
                fontSize: 15,
                color: const Color(0xFF374151),
                height: 1.6,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Post Actions
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: const Color(0xFFF8FAFC),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                _buildPostAction(
                  icon: post.isLiked ? Icons.favorite : Icons.favorite_border,
                  label: '${post.likes}',
                  color:
                      post.isLiked
                          ? const Color(0xFFEF4444)
                          : const Color(0xFF6B7280),
                  onTap: () => _toggleLike(post),
                ),
                const SizedBox(width: 24),
                _buildPostAction(
                  icon: Icons.chat_bubble_outline,
                  label: '${post.comments}',
                  color: const Color(0xFF6B7280),
                  onTap: () => _showComments(post),
                ),
                const SizedBox(width: 24),
                _buildPostAction(
                  icon: Icons.share_outlined,
                  label: '${post.shares}',
                  color: const Color(0xFF6B7280),
                  onTap: () => _sharePost(post),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostAction({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(width: 6),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  void _showCreatePostDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'إنشاء منشور جديد',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w700),
            ),
            content: TextField(
              maxLines: 4,
              decoration: InputDecoration(
                hintText: 'ما الذي تفكر فيه؟',
                hintStyle: GoogleFonts.cairo(),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إلغاء', style: GoogleFonts.cairo()),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: Text('نشر', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  void _toggleLike(Post post) {
    setState(() {
      post.isLiked = !post.isLiked;
      post.likes += post.isLiked ? 1 : -1;
    });
  }

  void _showComments(Post post) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('التعليقات', style: GoogleFonts.cairo()),
            content: Text('لا توجد تعليقات بعد', style: GoogleFonts.cairo()),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  void _sharePost(Post post) {
    setState(() {
      post.shares += 1;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم مشاركة المنشور', style: GoogleFonts.cairo()),
        backgroundColor: const Color(0xFF10B981),
      ),
    );
  }
}

// Profile Screen
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  bool _isDarkMode = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // Modern Profile Header
            SliverToBoxAdapter(
              child: Container(
                height: 280,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF667EEA),
                      const Color(0xFF764BA2),
                      const Color(0xFF6366F1),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(32),
                    bottomRight: Radius.circular(32),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // Background Pattern
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(32),
                            bottomRight: Radius.circular(32),
                          ),
                        ),
                        child: Container(),
                      ),
                    ),

                    // Profile Content
                    Positioned.fill(
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          children: [
                            const SizedBox(height: 20),
                            // Profile Avatar
                            Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.white.withValues(alpha: 0.2),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 3,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.person_rounded,
                                size: 50,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 20),
                            // Name and Info
                            Text(
                              'أحمد محمد علي',
                              style: GoogleFonts.cairo(
                                fontSize: 26,
                                fontWeight: FontWeight.w800,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                'طالب في كلية الشريعة والقانون',
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: Colors.white.withValues(alpha: 0.9),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            // Stats Row
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                _buildProfileStat('الفرقة', 'الثالثة'),
                                _buildProfileStat('المعدل', '3.8'),
                                _buildProfileStat('الساعات', '120'),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Settings Section
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الإعدادات',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildModernSettingItem(
                      icon: Icons.edit_rounded,
                      title: 'تعديل الملف الشخصي',
                      subtitle: 'تحديث البيانات الشخصية',
                      color: const Color(0xFF6366F1),
                      onTap: () {},
                    ),
                    _buildModernSettingItem(
                      icon: Icons.download_rounded,
                      title: 'التحميلات',
                      subtitle: 'الملفات والمحاضرات المحملة',
                      color: const Color(0xFF10B981),
                      onTap: () {},
                    ),
                    _buildModernSettingItem(
                      icon: Icons.notifications_rounded,
                      title: 'الإشعارات',
                      subtitle: 'إدارة الإشعارات والتنبيهات',
                      color: const Color(0xFFF59E0B),
                      onTap: () {},
                    ),
                    _buildModernSettingItem(
                      icon:
                          _isDarkMode
                              ? Icons.dark_mode_rounded
                              : Icons.light_mode_rounded,
                      title: 'الوضع المظلم',
                      subtitle: 'تغيير مظهر التطبيق',
                      color: const Color(0xFF8B5CF6),
                      trailing: Switch(
                        value: _isDarkMode,
                        onChanged: (value) {
                          setState(() {
                            _isDarkMode = value;
                          });
                        },
                        activeColor: const Color(0xFF8B5CF6),
                      ),
                      onTap: () {},
                    ),
                    const SizedBox(height: 20),
                    _buildModernSettingItem(
                      icon: Icons.logout_rounded,
                      title: 'تسجيل الخروج',
                      subtitle: 'الخروج من التطبيق',
                      color: const Color(0xFFEF4444),
                      onTap: () {},
                      isDestructive: true,
                    ),
                  ],
                ),
              ),
            ),

            // Bottom spacing
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? Colors.red : const Color(0xFF6366F1),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w600,
            color: isDestructive ? Colors.red : const Color(0xFF1F2937),
          ),
        ),
        trailing: trailing ?? Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  Widget _buildProfileStat(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.w800,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.8),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildModernSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        title: Text(
          title,
          style: GoogleFonts.cairo(
            fontWeight: FontWeight.w700,
            fontSize: 16,
            color:
                isDestructive
                    ? const Color(0xFFEF4444)
                    : const Color(0xFF1F2937),
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.cairo(
            fontSize: 13,
            color: const Color(0xFF6B7280),
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing:
            trailing ??
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: 16,
              color: const Color(0xFF9CA3AF),
            ),
        onTap: onTap,
      ),
    );
  }
}

// Year Details Screen
class YearDetailsScreen extends StatelessWidget {
  final AcademicYear year;

  const YearDetailsScreen({super.key, required this.year});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: ListView.builder(
        padding: const EdgeInsets.all(20),
        itemCount: year.semesters.length,
        itemBuilder: (context, index) {
          final semester = year.semesters[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: ExpansionTile(
              title: Text(
                semester.name,
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              children:
                  semester.subjects.map((subject) {
                    return Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF8FAFC),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: const Color(0xFFE5E7EB),
                          width: 1,
                        ),
                      ),
                      child: ListTile(
                        leading: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: year.color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(
                            Icons.book_rounded,
                            color: year.color,
                            size: 20,
                          ),
                        ),
                        title: Text(
                          subject.name,
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        subtitle: Text(
                          subject.code,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: const Color(0xFF6B7280),
                          ),
                        ),
                        trailing: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: year.color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '${subject.creditHours} ساعات',
                            style: GoogleFonts.cairo(
                              fontSize: 11,
                              fontWeight: FontWeight.w600,
                              color: year.color,
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          );
        },
      ),
    );
  }

  int _getTotalSubjects(AcademicYear year) {
    return year.semesters.fold(
      0,
      (total, semester) => total + semester.subjects.length,
    );
  }

  Widget _buildModernSemesterCard(Semester semester, int index, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Semester Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  color.withValues(alpha: 0.1),
                  color.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    Icons.calendar_today_rounded,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        semester.name,
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: const Color(0xFF1F2937),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${semester.subjects.length} مواد دراسية',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: const Color(0xFF6B7280),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_getTotalCreditHours(semester)} ساعة',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Subjects List
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children:
                  semester.subjects.asMap().entries.map((entry) {
                    final subjectIndex = entry.key;
                    final subject = entry.value;
                    return Padding(
                      padding: EdgeInsets.only(
                        bottom:
                            subjectIndex < semester.subjects.length - 1
                                ? 16
                                : 0,
                      ),
                      child: _buildModernSubjectCard(subject, color),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSubjectCard(Subject subject, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
      ),
      child: Row(
        children: [
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(Icons.book_rounded, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  subject.name,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subject.code,
                  style: GoogleFonts.cairo(
                    fontSize: 13,
                    color: const Color(0xFF6B7280),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
            ),
            child: Text(
              '${subject.creditHours} ساعات',
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  int _getTotalCreditHours(Semester semester) {
    return semester.subjects.fold(
      0,
      (total, subject) => total + subject.creditHours,
    );
  }
}
